import { Inject, Injectable } from '@nestjs/common';
import { DeletionResponse, DeletionResult } from '@vendure/common/lib/generated-types';
import { CustomFieldsObject, ID, PaginatedList } from '@vendure/common/lib/shared-types';
import {
    CustomFieldRelationService,
    ListQueryBuilder,
    ListQueryOptions,
    RelationPaths,
    RequestContext,
    TransactionalConnection,
    assertFound,
    patchEntity,
    Asset
} from '@vendure/core';
import { DECALS_PLUGIN_OPTIONS } from '../constants';
import { Decal, DecalColorOption } from '../entities/decal.entity';
import { PluginInitOptions } from '../types';

// These can be replaced by generated types if you set up code generation
interface CreateDecalInput {
    code: string;
    name: string;
    description?: string;
    price: number;
    colorOptions?: DecalColorOption[];
    imageIds?: ID[];
    enabled?: boolean;
    customFields?: CustomFieldsObject;
}
interface UpdateDecalInput {
    id: ID;
    code?: string;
    name?: string;
    description?: string;
    price?: number;
    colorOptions?: DecalColorOption[];
    imageIds?: ID[];
    enabled?: boolean;
    customFields?: CustomFieldsObject;
}

@Injectable()
export class DecalService {
    constructor(
        private connection: TransactionalConnection,
        private listQueryBuilder: ListQueryBuilder,
        private customFieldRelationService: CustomFieldRelationService, 
        @Inject(DECALS_PLUGIN_OPTIONS) private options: PluginInitOptions
    ) {}

    findAll(
        ctx: RequestContext,
        options?: ListQueryOptions<Decal>,
        relations?: RelationPaths<Decal>,
    ): Promise<PaginatedList<Decal>> {
        return this.listQueryBuilder
            .build(Decal, options, {
                relations: relations || ['images'],
                ctx,
            }
            ).getManyAndCount().then(([items, totalItems]) => {
                return {
                    items,
                    totalItems,
                }
            }
            );
    }

    findOne(
        ctx: RequestContext,
        id: ID,
        relations?: RelationPaths<Decal>,
    ): Promise<Decal | null> {
        return this.connection
            .getRepository(ctx, Decal)
            .findOne({
                where: { id },
                relations: relations || ['images'],
            });
    }

    async create(ctx: RequestContext, input: CreateDecalInput): Promise<Decal> {
        const { imageIds, ...rest } = input;
        
        const newEntity = new Decal({
            ...rest,
            enabled: input.enabled ?? true,
        });

        if (imageIds && imageIds.length > 0) {
            const images = await this.connection.getRepository(ctx, Asset).findByIds(imageIds);
            newEntity.images = images;
        }

        const savedEntity = await this.connection.getRepository(ctx, Decal).save(newEntity);
        await this.customFieldRelationService.updateRelations(ctx, Decal, input, savedEntity);
        return assertFound(this.findOne(ctx, savedEntity.id));
    }

    async update(ctx: RequestContext, input: UpdateDecalInput): Promise<Decal> {
        const entity = await this.connection.getEntityOrThrow(ctx, Decal, input.id);
        const { imageIds, ...rest } = input;
        
        const updatedEntity = patchEntity(entity, rest);

        if (imageIds !== undefined) {
            if (imageIds.length > 0) {
                const images = await this.connection.getRepository(ctx, Asset).findByIds(imageIds);
                updatedEntity.images = images;
            } else {
                updatedEntity.images = [];
            }
        }

        await this.connection.getRepository(ctx, Decal).save(updatedEntity, { reload: false });
        await this.customFieldRelationService.updateRelations(ctx, Decal, input, updatedEntity);
        return assertFound(this.findOne(ctx, updatedEntity.id));
    }

    async delete(ctx: RequestContext, id: ID): Promise<DeletionResponse> {
        const entity = await this.connection.getEntityOrThrow(ctx, Decal, id);
        try {
            await this.connection.getRepository(ctx, Decal).remove(entity);
            return {
                result: DeletionResult.DELETED,
            };
        } catch (e: any) {
            return {
                result: DeletionResult.NOT_DELETED,
                message: e.toString(),
            };
        }
    }

    async findEnabledDecals(ctx: RequestContext): Promise<Decal[]> {
        return this.connection
            .getRepository(ctx, Decal)
            .find({
                where: { enabled: true },
                relations: ['images'],
                order: { createdAt: 'DESC' },
            });
    }
}
