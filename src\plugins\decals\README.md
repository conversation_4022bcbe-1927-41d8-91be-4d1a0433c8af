# Decals Plugin

This plugin provides a decal customization system for Vendure, allowing customers to add multiple decals to products (like shirts) with custom colors and pricing.

## Features

- **Decal Management**: Create and manage decals with images, names, descriptions, and base prices
- **Color Options**: Each decal can have multiple color options with individual price modifiers
- **Custom Pricing**: Automatic price calculation that includes base product price + selected decals + color modifiers
- **Order Line Integration**: Selected decals are stored as custom fields on order lines

## Entities

### Decal Entity
- `code`: Unique identifier for the decal
- `name`: Display name
- `description`: Optional description
- `price`: Base price in smallest currency unit (cents)
- `colorOptions`: Array of color options with hex codes and price modifiers
- `images`: Associated asset images
- `enabled`: Whether the decal is available for selection

### Color Option Interface
```typescript
interface DecalColorOption {
    name: string;        // e.g., "Red", "Blue"
    hexCode: string;     // e.g., "#FF0000"
    priceModifier: number; // Additional cost in cents
}
```

## Usage

### 1. Creating Decals
Use the `DecalService` to create decals:

```typescript
const decal = await decalService.create(ctx, {
    code: 'dragon-decal',
    name: 'Dragon Decal',
    description: 'Fierce dragon design',
    price: 500, // $5.00 in cents
    colorOptions: [
        { name: 'Red', hexCode: '#FF0000', priceModifier: 0 },
        { name: 'Gold', hexCode: '#FFD700', priceModifier: 100 }, // +$1.00
    ],
    imageIds: ['asset-id-1', 'asset-id-2'],
    enabled: true
});
```

### 2. Adding Decals to Order Lines
When adding items to an order, include selected decals in the custom fields:

```typescript
const selectedDecals = [
    {
        decalId: 'decal-id-1',
        colorOption: {
            name: 'Red',
            hexCode: '#FF0000',
            priceModifier: 0
        }
    },
    {
        decalId: 'decal-id-2',
        colorOption: {
            name: 'Gold',
            hexCode: '#FFD700',
            priceModifier: 100
        }
    }
];

// Store as JSON string in OrderLine custom field
const customFields = {
    selectedDecals: JSON.stringify(selectedDecals)
};
```

### 3. Price Calculation
The `DecalPriceCalculationStrategy` automatically calculates the total price:
- Base product price: $20.00
- Decal 1 (Red): $5.00 + $0.00 (color modifier) = $5.00
- Decal 2 (Gold): $3.00 + $1.00 (color modifier) = $4.00
- **Total: $29.00**

### 4. Using the Pricing Service
For manual price calculations or validation:

```typescript
const totalDecalPrice = await decalPricingService.calculateDecalPrice(ctx, selectedDecals);
const totalItemPrice = await decalPricingService.calculateTotalItemPrice(
    ctx, 
    baseProductPrice, 
    selectedDecalsJson
);
```

## Custom Fields

The plugin automatically adds a custom field to OrderLine:
- `selectedDecals`: JSON string containing the selected decals and color options

## Services

- **DecalService**: CRUD operations for decals
- **DecalPricingService**: Price calculation utilities
- **DecalPriceCalculationStrategy**: Automatic price calculation for order items

## Integration Notes

1. The price calculation strategy is automatically applied to all order items
2. Only enabled decals are included in price calculations
3. Invalid JSON in selectedDecals custom field is gracefully handled (defaults to empty array)
4. Decal images are loaded as Asset relations for easy access in the storefront

## Example Storefront Integration

```typescript
// Fetch available decals
const decals = await decalService.findEnabledDecals(ctx);

// Display decals with color options to customer
// Customer selects decals and colors
// Add to cart with custom fields containing selected decals

const addToCartInput = {
    productVariantId: 'shirt-variant-id',
    quantity: 1,
    customFields: {
        selectedDecals: JSON.stringify(customerSelectedDecals)
    }
};
```

The total price will be automatically calculated including all selected decals and color modifiers.

## Configuration

### Enabling Automatic Price Calculation

To enable automatic price calculation that includes decal costs, configure the `DecalPriceCalculationStrategy` in your `vendure-config.ts`:

```typescript
import { DecalPriceCalculationStrategy } from './plugins/decals/services/decal-price-calculation.strategy';

export const config: VendureConfig = {
    // ... other config
    orderOptions: {
        orderItemPriceCalculationStrategy: DecalPriceCalculationStrategy,
    },
    // ... rest of config
};
```

**Note**: The strategy requires proper dependency injection setup. If you encounter issues, you can use the `DecalPricingService` manually in your resolvers or mutations instead. 