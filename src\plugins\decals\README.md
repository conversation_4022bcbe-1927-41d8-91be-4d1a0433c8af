# Decals Plugin

This plugin provides a decal customization system for Vendure, allowing customers to add decals to products (like shirts) with custom colors and pricing. It includes a comprehensive admin UI for managing decals.

## Features

- **Decal Management**: Create and manage decals with image, names, descriptions, and base prices
- **Admin UI**: Full CRUD interface for managing decals in the Vendure admin
- **Color Options**: Each decal can have multiple color options with individual price modifiers
- **Custom Pricing**: Automatic price calculation that includes base product price + selected decals + color modifiers
- **Order Line Integration**: Selected decals are stored as custom fields on order lines
- **GraphQL API**: Complete GraphQL API for both admin and shop operations

## Entities

### Decal Entity
- `code`: Unique identifier for the decal
- `name`: Display name
- `description`: Optional description
- `price`: Base price in smallest currency unit (Money type)
- `colorOptions`: Array of color options with hex codes and price modifiers
- `image`: Single associated asset image
- `enabled`: Whether the decal is available for selection

### Color Option Interface
```typescript
interface DecalColorOption {
    name: string;        // e.g., "Red", "Blue"
    hexCode: string;     // e.g., "#FF0000"
    priceModifier: number; // Additional cost in cents
}
```

## Usage

### 1. Admin UI Management
Access the decals management interface at `/admin/extensions/decals` in your Vendure admin panel. The interface provides:

- **List View**: Browse all decals with filtering, sorting, and search
- **Create/Edit Forms**: Comprehensive forms for decal management
- **Image Upload**: Single image per decal with asset picker
- **Color Options**: Dynamic color option management with hex codes and price modifiers
- **Bulk Operations**: Delete multiple decals at once

### 2. GraphQL API Usage

#### Admin API - Creating Decals
```graphql
mutation CreateDecal($input: CreateDecalInput!) {
    createDecal(input: $input) {
        id
        code
        name
        price
        image {
            preview
        }
        colorOptions {
            name
            hexCode
            priceModifier
        }
    }
}
```

#### Shop API - Fetching Available Decals
```graphql
query AvailableDecals {
    availableDecals {
        id
        code
        name
        description
        price
        image {
            preview
        }
        colorOptions {
            name
            hexCode
            priceModifier
        }
    }
}
```

### 3. Programmatic Usage
Use the `DecalService` to create decals programmatically:

```typescript
const decal = await decalService.create(ctx, {
    code: 'dragon-decal',
    name: 'Dragon Decal',
    description: 'Fierce dragon design',
    price: 500, // $5.00 in cents
    colorOptions: [
        { name: 'Red', hexCode: '#FF0000', priceModifier: 0 },
        { name: 'Gold', hexCode: '#FFD700', priceModifier: 100 }, // +$1.00
    ],
    imageId: 'asset-id-1', // Single image
    enabled: true
});
```

### 4. Adding Decals to Order Lines
When adding items to an order, include selected decals in the custom fields:

```typescript
const selectedDecals = [
    {
        decalId: 'decal-id-1',
        colorOption: {
            name: 'Red',
            hexCode: '#FF0000',
            priceModifier: 0
        }
    },
    {
        decalId: 'decal-id-2',
        colorOption: {
            name: 'Gold',
            hexCode: '#FFD700',
            priceModifier: 100
        }
    }
];

// Store as JSON string in OrderLine custom field
const customFields = {
    selectedDecals: JSON.stringify(selectedDecals)
};
```

### 3. Price Calculation
The `DecalPriceCalculationStrategy` automatically calculates the total price:
- Base product price: $20.00
- Decal 1 (Red): $5.00 + $0.00 (color modifier) = $5.00
- Decal 2 (Gold): $3.00 + $1.00 (color modifier) = $4.00
- **Total: $29.00**

### 4. Using the Pricing Service
For manual price calculations or validation:

```typescript
const totalDecalPrice = await decalPricingService.calculateDecalPrice(ctx, selectedDecals);
const totalItemPrice = await decalPricingService.calculateTotalItemPrice(
    ctx, 
    baseProductPrice, 
    selectedDecalsJson
);
```

## Custom Fields

The plugin automatically adds a custom field to OrderLine:
- `selectedDecals`: JSON string containing the selected decals and color options

## Admin UI Features

The plugin provides a comprehensive admin interface with the following features:

### Decal List View
- **Data Table**: Sortable, filterable table with pagination
- **Search**: Search by decal name or code
- **Filters**: Filter by enabled status, creation date, etc.
- **Bulk Actions**: Delete multiple decals at once
- **Quick Actions**: Edit and delete individual decals

### Decal Detail View
- **Form Validation**: Client-side validation for all fields
- **Image Management**: Single image upload with asset picker
- **Color Options**: Dynamic color option management
  - Color name input
  - Hex code input with color preview
  - Price modifier (can be positive or negative)
- **Price Input**: Currency-formatted price input
- **Enable/Disable**: Toggle decal availability

### Navigation
- **Breadcrumbs**: Clear navigation hierarchy
- **Route Management**: Proper routing for list, create, and edit views
- **Permissions**: Respects Vendure's permission system

## Services

- **DecalService**: CRUD operations for decals
- **DecalPricingService**: Price calculation utilities
- **DecalPriceCalculationStrategy**: Automatic price calculation for order items
- **DecalAdminResolver**: GraphQL resolver for admin API
- **DecalShopResolver**: GraphQL resolver for shop API

## Integration Notes

1. **Price Calculation**: The price calculation strategy is automatically applied to all order items
2. **Enabled Decals**: Only enabled decals are included in price calculations and shop API responses
3. **Error Handling**: Invalid JSON in selectedDecals custom field is gracefully handled (defaults to empty array)
4. **Image Management**: Decal image is loaded as Asset relation for easy access in the storefront
5. **Money Type**: Prices are stored using Vendure's Money type for proper currency handling
6. **Single Image**: Each decal supports one image (changed from multiple images for simplicity)
7. **GraphQL API**: Complete GraphQL API available for both admin and shop operations
8. **Permissions**: Admin operations respect Vendure's permission system (ReadCatalog, CreateCatalog, UpdateCatalog, DeleteCatalog)

## Example Storefront Integration

### Using GraphQL Shop API
```typescript
// Fetch available decals using GraphQL
const GET_AVAILABLE_DECALS = gql`
    query GetAvailableDecals {
        availableDecals {
            id
            code
            name
            description
            price
            image {
                preview
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

// Display decals with color options to customer
// Customer selects decals and colors
// Add to cart with custom fields containing selected decals

const addToCartInput = {
    productVariantId: 'shirt-variant-id',
    quantity: 1,
    customFields: {
        selectedDecals: JSON.stringify([
            {
                decalId: 'selected-decal-id',
                colorOption: {
                    name: 'Red',
                    hexCode: '#FF0000',
                    priceModifier: 0
                }
            }
        ])
    }
};
```

The total price will be automatically calculated including all selected decals and color modifiers.

## Configuration

### Enabling Automatic Price Calculation

To enable automatic price calculation that includes decal costs, configure the `DecalPriceCalculationStrategy` in your `vendure-config.ts`:

```typescript
import { DecalPriceCalculationStrategy } from './plugins/decals/services/decal-price-calculation.strategy';

export const config: VendureConfig = {
    // ... other config
    orderOptions: {
        orderItemPriceCalculationStrategy: DecalPriceCalculationStrategy,
    },
    // ... rest of config
};
```

**Note**: The strategy requires proper dependency injection setup. If you encounter issues, you can use the `DecalPricingService` manually in your resolvers or mutations instead. 