<vdr-page-block>
    <vdr-action-bar>
        <vdr-ab-left>
            <vdr-page-title [title]="'Decals'" icon="image"></vdr-page-title>
        </vdr-ab-left>
        <vdr-ab-right>
            <vdr-action-bar-items locationId="decal-list"></vdr-action-bar-items>
            <button
                class="btn btn-primary"
                type="button"
                (click)="create()"
                [disabled]="!('CreateCatalog' | hasPermission)"
            >
                <clr-icon shape="plus"></clr-icon>
                {{ 'common.create' | translate }}
            </button>
        </vdr-ab-right>
    </vdr-action-bar>

    <vdr-data-table-2
        id="decal-list"
        [items]="items$ | async"
        [itemsPerPage]="itemsPerPage$ | async"
        [totalItems]="totalItems$ | async"
        [currentPage]="currentPage$ | async"
        [filters]="filters"
        [sorts]="sorts"
        [loading]="loading$ | async"
        (pageChange)="setPageNumber($event)"
        (itemsPerPageChange)="setItemsPerPage($event)"
    >
        <vdr-bulk-action-menu
            locationId="decal-list"
            [hostComponent]="this"
            [selectionManager]="selectionManager"
        >
            <vdr-bulk-action-menu-item
                *ngIf="'DeleteCatalog' | hasPermission"
                [label]="'common.delete' | translate"
                [icon]="'trash'"
                (click)="deleteSelectedDecals()"
            ></vdr-bulk-action-menu-item>
        </vdr-bulk-action-menu>

        <vdr-dt2-search
            [searchTermControl]="searchTermControl"
            searchTermPlaceholder="Search by name or code..."
        ></vdr-dt2-search>

        <vdr-dt2-column [heading]="'common.id' | translate" id="id" [hiddenByDefault]="true">
            <ng-template let-decal="item">
                {{ decal.id }}
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-column [heading]="'common.image' | translate" id="image">
            <ng-template let-decal="item">
                <div class="image-placeholder">
                    <img
                        *ngIf="decal.image?.preview"
                        [src]="decal.image.preview + '?preset=tiny'"
                        class="decal-image"
                        alt="{{ decal.name }}"
                    />
                    <clr-icon *ngIf="!decal.image?.preview" shape="image" size="24"></clr-icon>
                </div>
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-column [heading]="'common.code' | translate" id="code" [optional]="false">
            <ng-template let-decal="item">
                <a class="button-ghost" [routerLink]="['./', decal.id]">
                    <span>{{ decal.code }}</span>
                    <clr-icon shape="arrow right"></clr-icon>
                </a>
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-column [heading]="'common.name' | translate" id="name" [optional]="false">
            <ng-template let-decal="item">
                <a class="button-ghost" [routerLink]="['./', decal.id]">
                    <span>{{ decal.name }}</span>
                    <clr-icon shape="arrow right"></clr-icon>
                </a>
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-column [heading]="'common.description' | translate" id="description">
            <ng-template let-decal="item">
                <span class="description-text">{{ decal.description || '-' }}</span>
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-column [heading]="'common.price' | translate" id="price" [optional]="false">
            <ng-template let-decal="item">
                <span class="price">{{ formatPrice(decal.price) }}</span>
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-column [heading]="'Color Options' | translate" id="colorOptions">
            <ng-template let-decal="item">
                <div class="color-options">
                    <span
                        *ngFor="let color of decal.colorOptions; let i = index"
                        class="color-chip"
                        [style.background-color]="color.hexCode"
                        [title]="color.name + (color.priceModifier ? ' (+' + formatPrice(color.priceModifier) + ')' : '')"
                    ></span>
                    <span *ngIf="!decal.colorOptions?.length" class="text-muted">-</span>
                </div>
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-column [heading]="'common.enabled' | translate" id="enabled">
            <ng-template let-decal="item">
                <vdr-chip [colorType]="decal.enabled ? 'success' : 'warning'">
                    {{ decal.enabled ? ('common.enabled' | translate) : ('common.disabled' | translate) }}
                </vdr-chip>
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-column [heading]="'common.updated-at' | translate" id="updatedAt">
            <ng-template let-decal="item">
                {{ decal.updatedAt | localeDate : 'short' }}
            </ng-template>
        </vdr-dt2-column>

        <vdr-dt2-row-action-menu>
            <ng-template let-decal="item">
                <button
                    type="button"
                    class="button-ghost"
                    [routerLink]="['./', decal.id]"
                    [disabled]="!('ReadCatalog' | hasPermission)"
                >
                    <clr-icon shape="edit"></clr-icon>
                    {{ 'common.edit' | translate }}
                </button>
                <button
                    type="button"
                    class="button-ghost"
                    (click)="deleteDecal(decal)"
                    [disabled]="!('DeleteCatalog' | hasPermission)"
                >
                    <clr-icon shape="trash"></clr-icon>
                    {{ 'common.delete' | translate }}
                </button>
            </ng-template>
        </vdr-dt2-row-action-menu>
    </vdr-data-table-2>
</vdr-page-block>
