import { Injectable } from '@nestjs/common';
import {
    Order,
    OrderItemPriceCalculationStrategy,
    PriceCalculationResult,
    ProductVariant,
    RequestContext,
    TransactionalConnection,
} from '@vendure/core';
import { Decal } from '../entities/decal.entity';
import { SelectedDecal } from '../types';

@Injectable()
export class DecalPriceCalculationStrategy implements OrderItemPriceCalculationStrategy {
    constructor(private connection: TransactionalConnection) {}

    async calculateUnitPrice(
        ctx: RequestContext,
        productVariant: ProductVariant,
        orderLineCustomFields: { [key: string]: any },
        order: Order,
        quantity: number,
    ): Promise<PriceCalculationResult> {
        // Start with the base product variant price
        let basePrice = productVariant.price;

        // Get selected decals from custom fields
        let selectedDecals: SelectedDecal[] = [];
        
        // Parse JSON string if it exists
        if (orderLineCustomFields.selectedDecals) {
            try {
                selectedDecals = typeof orderLineCustomFields.selectedDecals === 'string' 
                    ? JSON.parse(orderLineCustomFields.selectedDecals)
                    : orderLineCustomFields.selectedDecals;
            } catch (error) {
                // If parsing fails, default to empty array
                selectedDecals = [];
            }
        }
        
        if (selectedDecals.length > 0) {
            // Calculate additional cost from decals
            let decalsCost = 0;
            
            // Get all decal IDs to query at once
            const decalIds = selectedDecals.map(sd => sd.decalId);
            
            if (decalIds.length > 0) {
                const decals = await this.connection
                    .getRepository(ctx, Decal)
                    .findByIds(decalIds);
                
                // Create a map for quick lookup
                const decalMap = new Map(decals.map(d => [d.id.toString(), d]));
                
                // Calculate total decals cost
                for (const selectedDecal of selectedDecals) {
                    const decal = decalMap.get(selectedDecal.decalId.toString());
                    if (decal && decal.enabled) {
                        // Add base decal price
                        decalsCost += decal.price;
                        
                        // Add color option price modifier if applicable
                        if (selectedDecal.colorOption) {
                            decalsCost += selectedDecal.colorOption.priceModifier;
                        }
                    }
                }
            }
            
            basePrice += decalsCost;
        }

        return {
            price: basePrice,
            priceIncludesTax: productVariant.listPriceIncludesTax,
        };
    }
} 