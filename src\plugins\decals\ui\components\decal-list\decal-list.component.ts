import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
    DataService,
    ItemOf,
    LanguageCode,
    LogicalOperator,
    ModalService,
    NotificationService,
    SharedModule,
    SortOrder,
    TypedBaseListComponent,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';

export const GET_DECAL_LIST = gql`
    query GetDecalList($options: DecalListOptions) {
        decals(options: $options) {
            items {
                id
                createdAt
                updatedAt
                code
                name
                description
                price
                enabled
                image {
                    id
                    preview
                }
                colorOptions {
                    name
                    hexCode
                    priceModifier
                }
            }
            totalItems
        }
    }
`;

export const DELETE_DECAL = gql`
    mutation DeleteDecal($id: ID!) {
        deleteDecal(id: $id) {
            result
            message
        }
    }
`;

export const DELETE_DECALS = gql`
    mutation DeleteDecals($ids: [ID!]!) {
        deleteDecals(ids: $ids) {
            result
            message
        }
    }
`;

type DecalListItem = ItemOf<typeof GET_DECAL_LIST, 'decals'>;

@Component({
    selector: 'vdr-decal-list',
    templateUrl: './decal-list.component.html',
    standalone: true,
    imports: [SharedModule],
})
export class DecalListComponent extends TypedBaseListComponent<typeof GET_DECAL_LIST, 'decals'> implements OnInit {
    readonly customFields = this.serverConfigService.getCustomFieldsFor('Decal');
    readonly filters = this.createFilterCollection()
        .addIdFilter()
        .addDateFilters()
        .addFilter({
            name: 'enabled',
            type: { kind: 'boolean' },
            label: _('common.enabled'),
            filterField: 'enabled',
        })
        .addFilter({
            name: 'name',
            type: { kind: 'text' },
            label: _('common.name'),
            filterField: 'name',
        })
        .addFilter({
            name: 'code',
            type: { kind: 'text' },
            label: _('common.code'),
            filterField: 'code',
        })
        .connectToRoute(this.route);

    readonly sorts = this.createSortCollection()
        .defaultSort('createdAt', 'DESC')
        .addSort({ name: 'createdAt' })
        .addSort({ name: 'updatedAt' })
        .addSort({ name: 'name' })
        .addSort({ name: 'code' })
        .addSort({ name: 'price' })
        .addSort({ name: 'enabled' })
        .connectToRoute(this.route);

    constructor(
        protected dataService: DataService,
        private router: Router,
        private modalService: ModalService,
        private notificationService: NotificationService,
    ) {
        super();
        super.configure({
            document: GET_DECAL_LIST,
            getItems: data => data.decals,
            setVariables: (skip, take) => ({
                options: {
                    skip,
                    take,
                    filter: {
                        name: { contains: this.searchTermControl.value },
                        ...this.filters.createFilterInput(),
                    },
                    sort: this.sorts.createSortInput(),
                },
            }),
            refreshListOnChanges: [this.filters.valueChanges, this.sorts.valueChanges],
        });
    }

    ngOnInit() {
        super.ngOnInit();
    }

    create() {
        this.router.navigate(['./create'], { relativeTo: this.route });
    }

    async deleteDecal(decal: DecalListItem) {
        const result = await this.modalService.dialog({
            title: _('common.confirm-delete-x', { entity: 'Decal' }),
            body: _('common.are-you-sure-you-want-to-delete'),
            buttons: [
                { type: 'secondary', label: _('common.cancel') },
                { type: 'danger', label: _('common.delete'), returnValue: true },
            ],
        }).result;

        if (result) {
            this.dataService.mutate(DELETE_DECAL, { id: decal.id }).subscribe({
                next: () => {
                    this.notificationService.success(_('common.notify-delete-success', { entity: 'Decal' }));
                    this.refresh();
                },
                error: (err) => {
                    this.notificationService.error(_('common.notify-delete-error', { entity: 'Decal' }));
                },
            });
        }
    }

    async deleteSelectedDecals() {
        const selectedIds = this.selectionManager.getSelectedIds();
        if (selectedIds.length === 0) return;

        const result = await this.modalService.dialog({
            title: _('common.confirm-bulk-delete-x', { count: selectedIds.length }),
            body: _('common.are-you-sure-you-want-to-delete'),
            buttons: [
                { type: 'secondary', label: _('common.cancel') },
                { type: 'danger', label: _('common.delete'), returnValue: true },
            ],
        }).result;

        if (result) {
            this.dataService.mutate(DELETE_DECALS, { ids: selectedIds }).subscribe({
                next: () => {
                    this.notificationService.success(_('common.notify-delete-success', { entity: 'Decals' }));
                    this.selectionManager.clearSelection();
                    this.refresh();
                },
                error: (err) => {
                    this.notificationService.error(_('common.notify-delete-error', { entity: 'Decals' }));
                },
            });
        }
    }

    formatPrice(price: number): string {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price / 100);
    }
}
