import * as path from 'path';
import { AdminUiExtension } from '@vendure/ui-devkit/compiler';
import { PluginCommonModule, Type, VendurePlugin, LanguageCode } from '@vendure/core';

import { DECALS_PLUGIN_OPTIONS } from './constants';
import { PluginInitOptions } from './types';
import { Decal } from './entities/decal.entity';
import { DecalService } from './services/decal.service';
import { DecalPriceCalculationStrategy } from './services/decal-price-calculation.strategy';
import { DecalPricingService } from './services/decal-pricing.service';

@VendurePlugin({
    imports: [PluginCommonModule],
    providers: [
        { provide: DECALS_PLUGIN_OPTIONS, useFactory: () => DecalsPlugin.options }, 
        DecalService,
        DecalPriceCalculationStrategy,
        DecalPricingService,
    ],
    configuration: config => {
        // Configure custom fields for OrderLine to store selected decals
        config.customFields.OrderLine = [
            ...(config.customFields.OrderLine || []),
            {
                name: 'selectedDecals',
                type: 'string',
                list: false,
                label: [{ languageCode: LanguageCode.en, value: 'Selected Decals' }],
                description: [{ 
                    languageCode: LanguageCode.en, 
                    value: 'JSON string containing selected decals and color options' 
                }],
                public: true,
                nullable: true,
            }
        ];

        return config;
    },
    compatibility: '^3.0.0',
    entities: [Decal],
})
export class DecalsPlugin {
    static options: PluginInitOptions;

    static init(options: PluginInitOptions): Type<DecalsPlugin> {
        this.options = options;
        return DecalsPlugin;
    }

    static ui: AdminUiExtension = {
        id: 'decals-ui',
        extensionPath: path.join(__dirname, 'ui'),
        routes: [{ route: 'decals', filePath: 'routes.ts' }],
        providers: ['providers.ts'],
    };
}
