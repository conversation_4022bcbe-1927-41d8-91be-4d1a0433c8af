<vdr-page-block>
    <vdr-action-bar>
        <vdr-ab-left>
            <vdr-page-title
                [title]="isNew ? ('Create Decal' | translate) : (entity$ | async)?.name"
                [subtitle]="isNew ? '' : (entity$ | async)?.code"
                icon="image"
            ></vdr-page-title>
        </vdr-ab-left>
        <vdr-ab-right>
            <vdr-action-bar-items locationId="decal-detail"></vdr-action-bar-items>
            <button
                class="btn btn-primary"
                type="button"
                (click)="isNew ? create() : save()"
                [disabled]="detailForm.invalid || detailForm.pristine"
            >
                <clr-icon shape="floppy"></clr-icon>
                {{ (isNew ? 'common.create' : 'common.update') | translate }}
            </button>
        </vdr-ab-right>
    </vdr-action-bar>

    <form class="form" [formGroup]="detailForm">
        <vdr-form-field [label]="'common.code' | translate" for="code">
            <input
                id="code"
                type="text"
                formControlName="code"
                [readonly]="!isNew"
                (input)="detailForm.markAsDirty()"
            />
        </vdr-form-field>

        <vdr-form-field [label]="'common.name' | translate" for="name">
            <input
                id="name"
                type="text"
                formControlName="name"
                (input)="detailForm.markAsDirty()"
            />
        </vdr-form-field>

        <vdr-form-field [label]="'common.description' | translate" for="description">
            <textarea
                id="description"
                formControlName="description"
                rows="3"
                (input)="detailForm.markAsDirty()"
            ></textarea>
        </vdr-form-field>

        <vdr-form-field [label]="'common.price' | translate" for="price">
            <div class="input-group">
                <span class="input-group-text">$</span>
                <input
                    id="price"
                    type="number"
                    step="0.01"
                    min="0"
                    formControlName="price"
                    (input)="detailForm.markAsDirty()"
                />
            </div>
        </vdr-form-field>

        <vdr-form-field [label]="'Image' | translate" for="image">
            <vdr-asset-picker-dialog
                [assets]="detailForm.get('imageId')?.value ? [detailForm.get('imageId')?.value] : []"
                (selectionChange)="detailForm.get('imageId')?.setValue($event[0]?.id || null); detailForm.markAsDirty()"
            ></vdr-asset-picker-dialog>
        </vdr-form-field>

        <vdr-form-field [label]="'common.enabled' | translate" for="enabled">
            <vdr-toggle
                formControlName="enabled"
                (change)="detailForm.markAsDirty()"
            ></vdr-toggle>
        </vdr-form-field>

        <vdr-card [title]="'Color Options' | translate">
            <div formArrayName="colorOptions">
                <div
                    *ngFor="let colorOption of colorOptionsFormArray.controls; let i = index"
                    [formGroupName]="i"
                    class="color-option-row"
                >
                    <vdr-form-field [label]="'Color Name' | translate" [for]="'colorName' + i">
                        <input
                            [id]="'colorName' + i"
                            type="text"
                            formControlName="name"
                            placeholder="e.g., Red, Blue"
                            (input)="detailForm.markAsDirty()"
                        />
                    </vdr-form-field>

                    <vdr-form-field [label]="'Hex Code' | translate" [for]="'hexCode' + i">
                        <div class="hex-input-group">
                            <input
                                [id]="'hexCode' + i"
                                type="text"
                                formControlName="hexCode"
                                placeholder="#000000"
                                pattern="^#[0-9A-Fa-f]{6}$"
                                (input)="detailForm.markAsDirty()"
                            />
                            <div
                                class="color-preview"
                                [style.background-color]="colorOption.get('hexCode')?.value"
                            ></div>
                        </div>
                    </vdr-form-field>

                    <vdr-form-field [label]="'Price Modifier' | translate" [for]="'priceModifier' + i">
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input
                                [id]="'priceModifier' + i"
                                type="number"
                                step="0.01"
                                formControlName="priceModifier"
                                placeholder="0.00"
                                (input)="detailForm.markAsDirty()"
                            />
                        </div>
                    </vdr-form-field>

                    <button
                        type="button"
                        class="btn btn-sm btn-secondary"
                        (click)="removeColorOption(i)"
                    >
                        <clr-icon shape="trash"></clr-icon>
                        {{ 'common.remove' | translate }}
                    </button>
                </div>

                <button
                    type="button"
                    class="btn btn-sm btn-secondary"
                    (click)="addColorOption()"
                >
                    <clr-icon shape="plus"></clr-icon>
                    {{ 'Add Color Option' | translate }}
                </button>
            </div>
        </vdr-card>
    </form>
</vdr-page-block>
