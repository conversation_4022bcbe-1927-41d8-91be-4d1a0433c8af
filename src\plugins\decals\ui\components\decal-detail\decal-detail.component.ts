import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
    BaseDetailComponent,
    DataService,
    LanguageCode,
    NotificationService,
    Permission,
    SharedModule,
    TypedBaseDetailComponent,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';
import { Observable } from 'rxjs';

export const GET_DECAL = gql`
    query GetDecal($id: ID!) {
        decal(id: $id) {
            id
            createdAt
            updatedAt
            code
            name
            description
            price
            enabled
            image {
                id
                name
                preview
                source
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

export const CREATE_DECAL = gql`
    mutation CreateDecal($input: CreateDecalInput!) {
        createDecal(input: $input) {
            id
            code
            name
            description
            price
            enabled
            image {
                id
                preview
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

export const UPDATE_DECAL = gql`
    mutation UpdateDecal($input: UpdateDecalInput!) {
        updateDecal(input: $input) {
            id
            code
            name
            description
            price
            enabled
            image {
                id
                preview
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

@Component({
    selector: 'vdr-decal-detail',
    templateUrl: './decal-detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [SharedModule],
})
export class DecalDetailComponent extends TypedBaseDetailComponent<typeof GET_DECAL, 'decal'> implements OnInit, OnDestroy {
    readonly customFields = this.serverConfigService.getCustomFieldsFor('Decal');
    detailForm: FormGroup;
    readonly updatePermission = [Permission.UpdateCatalog];

    constructor(
        private changeDetector: ChangeDetectorRef,
        protected dataService: DataService,
        private formBuilder: FormBuilder,
        private notificationService: NotificationService,
        private router: Router,
        private route: ActivatedRoute,
    ) {
        super();
        this.configure({
            document: GET_DECAL,
            entity: 'decal',
        });
        this.detailForm = this.formBuilder.group({
            code: ['', Validators.required],
            name: ['', Validators.required],
            description: [''],
            price: [0, [Validators.required, Validators.min(0)]],
            enabled: [true],
            imageId: [null],
            colorOptions: this.formBuilder.array([]),
        });
    }

    ngOnInit() {
        this.init();
    }

    ngOnDestroy() {
        this.destroy();
    }

    get colorOptionsFormArray(): FormArray {
        return this.detailForm.get('colorOptions') as FormArray;
    }

    protected setFormValues(entity: NonNullable<typeof GET_DECAL['decal']>, languageCode: LanguageCode): void {
        this.detailForm.patchValue({
            code: entity.code,
            name: entity.name,
            description: entity.description,
            price: entity.price / 100, // Convert from cents to dollars for display
            enabled: entity.enabled,
            imageId: entity.image?.id,
        });

        // Clear existing color options
        while (this.colorOptionsFormArray.length !== 0) {
            this.colorOptionsFormArray.removeAt(0);
        }

        // Add color options
        if (entity.colorOptions) {
            entity.colorOptions.forEach(option => {
                this.addColorOption(option);
            });
        }
    }

    create(): void {
        if (!this.detailForm.valid) {
            this.markFormGroupTouched(this.detailForm);
            return;
        }

        const formValue = this.detailForm.value;
        const input = {
            code: formValue.code,
            name: formValue.name,
            description: formValue.description,
            price: Math.round(formValue.price * 100), // Convert to cents
            enabled: formValue.enabled,
            imageId: formValue.imageId,
            colorOptions: formValue.colorOptions?.map((option: any) => ({
                name: option.name,
                hexCode: option.hexCode,
                priceModifier: Math.round(option.priceModifier * 100), // Convert to cents
            })) || [],
        };

        this.dataService.mutate(CREATE_DECAL, { input }).subscribe({
            next: (result) => {
                this.notificationService.success(_('common.notify-create-success', { entity: 'Decal' }));
                this.router.navigate(['../', result.createDecal.id], { relativeTo: this.route });
            },
            error: (err) => {
                this.notificationService.error(_('common.notify-create-error', { entity: 'Decal' }));
            },
        });
    }

    save(): void {
        if (!this.detailForm.valid) {
            this.markFormGroupTouched(this.detailForm);
            return;
        }

        const formValue = this.detailForm.value;
        const input = {
            id: this.id,
            code: formValue.code,
            name: formValue.name,
            description: formValue.description,
            price: Math.round(formValue.price * 100), // Convert to cents
            enabled: formValue.enabled,
            imageId: formValue.imageId,
            colorOptions: formValue.colorOptions?.map((option: any) => ({
                name: option.name,
                hexCode: option.hexCode,
                priceModifier: Math.round(option.priceModifier * 100), // Convert to cents
            })) || [],
        };

        this.dataService.mutate(UPDATE_DECAL, { input }).subscribe({
            next: (result) => {
                this.notificationService.success(_('common.notify-update-success', { entity: 'Decal' }));
                this.detailForm.markAsPristine();
                this.changeDetector.markForCheck();
            },
            error: (err) => {
                this.notificationService.error(_('common.notify-update-error', { entity: 'Decal' }));
            },
        });
    }

    addColorOption(option?: any): void {
        const colorOptionGroup = this.formBuilder.group({
            name: [option?.name || '', Validators.required],
            hexCode: [option?.hexCode || '#000000', [Validators.required, Validators.pattern(/^#[0-9A-Fa-f]{6}$/)]],
            priceModifier: [option?.priceModifier ? option.priceModifier / 100 : 0, Validators.required],
        });
        this.colorOptionsFormArray.push(colorOptionGroup);
    }

    removeColorOption(index: number): void {
        this.colorOptionsFormArray.removeAt(index);
    }

    protected getCustomFieldConfig(key: string): any {
        return this.customFields.find(x => x.name === key);
    }

    private markFormGroupTouched(formGroup: FormGroup): void {
        Object.keys(formGroup.controls).forEach(key => {
            const control = formGroup.get(key);
            control?.markAsTouched();

            if (control instanceof FormGroup) {
                this.markFormGroupTouched(control);
            }
        });
    }
}
