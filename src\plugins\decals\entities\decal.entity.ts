import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>Fields,
    VendureEntity,
    Asset
} from '@vendure/core';
import { Column, Entity, ManyToMany, JoinTable } from 'typeorm';

export class DecalCustomFields {}

export interface DecalColorOption {
    name: string;
    hexCode: string;
    priceModifier: number; // Additional cost for this color option
}

@Entity()
export class Decal extends VendureEntity implements HasCustomFields {
    constructor(input?: DeepPartial<Decal>) {
        super(input);
    }

    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'int' })
    price: number; // Price in smallest currency unit (cents)

    @Column('simple-json', { nullable: true })
    colorOptions: DecalColorOption[];

    @ManyToMany(() => Asset)
    @JoinTable()
    images: Asset[];

    @Column({ default: true })
    enabled: boolean;

    @Column(type => DecalCustomFields)
    customFields: DecalCustomFields;
}
