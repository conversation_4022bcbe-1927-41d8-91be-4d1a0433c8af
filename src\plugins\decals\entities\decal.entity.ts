import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>Fields,
    VendureEntity,
    Asset,
    Money
} from '@vendure/core';
import { Column, Entity, ManyToOne, JoinColumn } from 'typeorm';

export class DecalCustomFields {}

export interface DecalColorOption {
    name: string;
    hexCode: string;
    priceModifier: number; // Additional cost for this color option in smallest currency unit
}

@Entity()
export class Decal extends VendureEntity implements HasCustomFields {
    constructor(input?: DeepPartial<Decal>) {
        super(input);
    }

    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'int' })
    price: Money; // Price in smallest currency unit (cents)

    @Column('simple-json', { nullable: true })
    colorOptions: DecalColorOption[];

    @ManyToOne(() => Asset, { nullable: true })
    @JoinColumn()
    image: Asset | null;

    @Column({ default: true })
    enabled: boolean;

    @Column(type => DecalCustomFields)
    customFields: Decal<PERSON>ustomFields;
}
